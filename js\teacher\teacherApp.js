/**
 * ENGLISH 2025 - Teacher Portal Application
 * Handles teacher-specific functionality and dashboard interactions
 */

class TeacherApp {
    constructor() {
        this.currentUser = this.getCurrentUser();
        this.init();
    }

    /**
     * Initialize the teacher application
     */
    init() {
        this.setupEventListeners();
        this.loadDashboardData();
        this.animateOnLoad();
        this.checkAuthentication();
    }

    /**
     * Check if user is authenticated and has teacher role
     */
    checkAuthentication() {
        const selectedRole = sessionStorage.getItem('selectedRole');
        if (selectedRole !== 'teacher') {
            console.warn('Unauthorized access to teacher portal');
            window.location.href = '../index.html';
            return;
        }
    }

    /**
     * Get current user information
     */
    getCurrentUser() {
        // Mock teacher data - in real app this would come from authentication
        return {
            id: 'teacher_001',
            name: '<PERSON>',
            email: '<EMAIL>',
            schedule: [
                {
                    id: 'class_001',
                    time: '10:00 AM',
                    duration: '60 min',
                    title: 'Intermediate Conversation',
                    level: 'B1',
                    students: 6,
                    status: 'current'
                },
                {
                    id: 'class_002',
                    time: '2:00 PM',
                    duration: '60 min',
                    title: 'Travel & Tourism',
                    level: 'B2',
                    students: 4,
                    status: 'upcoming'
                },
                {
                    id: 'class_003',
                    time: '4:00 PM',
                    duration: '60 min',
                    title: 'Business English',
                    level: 'C1',
                    students: 3,
                    status: 'scheduled'
                }
            ],
            students: [
                {
                    id: 'student_001',
                    name: 'John Doe',
                    level: 'B1',
                    unit: 3,
                    progress: 75,
                    status: 'active',
                    lastActivity: '2 hours ago'
                },
                {
                    id: 'student_002',
                    name: 'Maria Garcia',
                    level: 'A2',
                    unit: 2,
                    progress: 45,
                    status: 'needs-attention',
                    lastActivity: '1 day ago'
                },
                {
                    id: 'student_003',
                    name: 'Ahmed Hassan',
                    level: 'B2',
                    unit: 4,
                    progress: 90,
                    status: 'excellent',
                    lastActivity: '30 min ago'
                }
            ],
            classesThisMonth: 18,
            averageRating: 4.8,
            feedbackGiven: 45
        };
    }

    /**
     * Set up event listeners for teacher portal interactions
     */
    setupEventListeners() {
        // Schedule item buttons
        const scheduleButtons = document.querySelectorAll('.schedule-item .btn');
        scheduleButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const scheduleItem = e.target.closest('.schedule-item');
                const classTitle = scheduleItem.querySelector('.class-info h4').textContent;
                this.handleScheduleAction(e.target.textContent.trim(), classTitle);
            });
        });

        // Student management buttons
        const studentButtons = document.querySelectorAll('.students-card .btn');
        studentButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                this.handleStudentAction(btn.textContent.trim());
            });
        });

        // Feedback buttons
        const feedbackButtons = document.querySelectorAll('.feedback-card .btn');
        feedbackButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                this.handleFeedbackAction(btn.textContent.trim());
            });
        });

        // Resource items
        const resourceItems = document.querySelectorAll('.resource-item');
        resourceItems.forEach(item => {
            item.addEventListener('click', () => {
                const resourceTitle = item.querySelector('h4').textContent;
                this.handleResourceAccess(resourceTitle);
            });
        });

        // Quick action buttons
        const actionButtons = document.querySelectorAll('.action-btn');
        actionButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const actionText = btn.textContent.trim();
                this.handleQuickAction(actionText);
            });
        });

        // Navigation links
        const navLinks = document.querySelectorAll('.portal-nav .nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleNavigation(link.getAttribute('href'));
            });
        });
    }

    /**
     * Load and display dashboard data
     */
    loadDashboardData() {
        this.updateWelcomeMessage();
        this.updateTeacherStats();
        this.updateSchedule();
        this.updateStudentProgress();
        this.updatePerformanceMetrics();
    }

    /**
     * Update welcome message with teacher name and stats
     */
    updateWelcomeMessage() {
        const welcomeTitle = document.querySelector('.welcome-content h2');
        const welcomeText = document.querySelector('.welcome-content p');
        
        if (welcomeTitle) {
            const hour = new Date().getHours();
            let greeting = 'Good morning';
            if (hour >= 12 && hour < 18) greeting = 'Good afternoon';
            if (hour >= 18) greeting = 'Good evening';
            
            welcomeTitle.textContent = `${greeting}, ${this.currentUser.name.split(' ')[0]}!`;
        }
        
        if (welcomeText) {
            const todayClasses = this.currentUser.schedule.length;
            const activeStudents = this.currentUser.students.length;
            welcomeText.innerHTML = `You have <strong>${todayClasses} classes</strong> scheduled today and <strong>${activeStudents} active students</strong> in your roster.`;
        }
    }

    /**
     * Update teacher statistics
     */
    updateTeacherStats() {
        const statNumbers = document.querySelectorAll('.teacher-stats .stat-number');
        
        if (statNumbers[0]) {
            statNumbers[0].textContent = this.currentUser.students.length;
        }
        
        if (statNumbers[1]) {
            statNumbers[1].textContent = this.currentUser.schedule.length;
        }
        
        if (statNumbers[2]) {
            statNumbers[2].textContent = this.currentUser.averageRating;
        }
    }

    /**
     * Update schedule display
     */
    updateSchedule() {
        // Schedule is already populated in HTML with mock data
        // In real app, this would dynamically populate from this.currentUser.schedule
    }

    /**
     * Update student progress display
     */
    updateStudentProgress() {
        // Student progress is already populated in HTML with mock data
        // In real app, this would dynamically populate from this.currentUser.students
    }

    /**
     * Update performance metrics
     */
    updatePerformanceMetrics() {
        const performanceItems = document.querySelectorAll('.performance-item .performance-value');
        
        if (performanceItems[0]) {
            performanceItems[0].textContent = this.currentUser.classesThisMonth;
        }
        
        if (performanceItems[1]) {
            performanceItems[1].textContent = '92%'; // Mock attendance rate
        }
        
        if (performanceItems[2]) {
            performanceItems[2].textContent = `${this.currentUser.averageRating}/5`;
        }
        
        if (performanceItems[3]) {
            performanceItems[3].textContent = this.currentUser.feedbackGiven;
        }
    }

    /**
     * Handle schedule-related actions
     */
    handleScheduleAction(action, className) {
        const actions = {
            'Join Class': () => {
                this.showNotification(`Joining ${className}...`, 'info');
                setTimeout(() => {
                    this.showNotification('Class session started!', 'success');
                }, 1500);
            },
            'Prepare': () => {
                this.showNotification(`Loading preparation materials for ${className}...`, 'info');
                setTimeout(() => {
                    this.showNotification('Preparation materials loaded!', 'success');
                }, 1000);
            },
            'View Details': () => {
                this.showNotification(`Loading details for ${className}...`, 'info');
                setTimeout(() => {
                    this.showNotification('Class details loaded!', 'success');
                }, 1000);
            }
        };

        if (actions[action]) {
            actions[action]();
        } else {
            this.showNotification(`${action} feature coming soon!`, 'info');
        }
    }

    /**
     * Handle student-related actions
     */
    handleStudentAction(action) {
        const actions = {
            'View All Students': () => {
                this.showNotification('Loading complete student roster...', 'info');
                setTimeout(() => {
                    this.showNotification('Student roster loaded!', 'success');
                }, 1000);
            }
        };

        if (actions[action]) {
            actions[action]();
        } else {
            this.showNotification(`${action} feature coming soon!`, 'info');
        }
    }

    /**
     * Handle feedback-related actions
     */
    handleFeedbackAction(action) {
        const actions = {
            'Add New Feedback': () => {
                this.showNotification('Opening feedback form...', 'info');
                setTimeout(() => {
                    this.showNotification('Feedback form ready!', 'success');
                }, 1000);
            }
        };

        if (actions[action]) {
            actions[action]();
        } else {
            this.showNotification(`${action} feature coming soon!`, 'info');
        }
    }

    /**
     * Handle resource access
     */
    handleResourceAccess(resourceTitle) {
        this.showNotification(`Loading ${resourceTitle}...`, 'info');
        
        setTimeout(() => {
            this.showNotification(`${resourceTitle} loaded successfully!`, 'success');
        }, 1000);
    }

    /**
     * Handle quick action buttons
     */
    handleQuickAction(action) {
        const actions = {
            'Schedule Makeup Class': () => this.showNotification('Opening makeup class scheduler...', 'info'),
            'Create Assignment': () => this.showNotification('Opening assignment creator...', 'info'),
            'Send Announcement': () => this.showNotification('Opening announcement composer...', 'info'),
            'Generate Report': () => this.showNotification('Generating performance report...', 'info')
        };

        if (actions[action]) {
            actions[action]();
            setTimeout(() => {
                this.showNotification(`${action} completed!`, 'success');
            }, 1500);
        } else {
            this.showNotification(`${action} feature coming soon!`, 'info');
        }
    }

    /**
     * Handle navigation
     */
    handleNavigation(href) {
        const section = href.replace('#', '');
        this.showNotification(`Navigating to ${section}...`, 'info');
        
        // In real app, this would show different sections of the portal
        setTimeout(() => {
            this.showNotification(`${section} section loaded!`, 'success');
        }, 1000);
    }

    /**
     * Show notification to user
     */
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // Add styles if not already added
        this.addNotificationStyles();

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);

        // Add close button functionality
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });
    }

    /**
     * Add notification styles
     */
    addNotificationStyles() {
        if (document.getElementById('notification-styles')) return;

        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                padding: 1rem;
                border-radius: 0.5rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                animation: slideIn 0.3s ease-out;
            }
            .notification-info {
                background: var(--info-color);
                color: white;
            }
            .notification-success {
                background: var(--success-color);
                color: white;
            }
            .notification-warning {
                background: var(--warning-color);
                color: white;
            }
            .notification-error {
                background: var(--error-color);
                color: white;
            }
            .notification-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .notification-close {
                background: none;
                border: none;
                color: inherit;
                font-size: 1.2rem;
                cursor: pointer;
                padding: 0;
                margin-left: 1rem;
            }
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Animate elements on page load
     */
    animateOnLoad() {
        const cards = document.querySelectorAll('.dashboard-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100 + (index * 100));
        });
    }
}

// Initialize the teacher application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.teacherApp = new TeacherApp();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TeacherApp;
}
