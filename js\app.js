/**
 * ENGLISH 2025 - Main Application Controller
 * Handles role selection, navigation, and core app functionality
 */

class App {
    constructor() {
        this.currentUser = null;
        this.loadingOverlay = document.getElementById('loading-overlay');
        this.init();
    }

    /**
     * Initialize the application
     */
    init() {
        this.setupEventListeners();
        this.checkExistingSession();
        this.animateOnLoad();
    }

    /**
     * Set up event listeners for role selection and navigation
     */
    setupEventListeners() {
        // Role selection buttons
        const roleButtons = document.querySelectorAll('.role-button');
        roleButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                const role = e.target.getAttribute('data-role');
                this.handleRoleSelection(role);
            });
        });

        // Role cards - make entire card clickable
        const roleCards = document.querySelectorAll('.role-card');
        roleCards.forEach(card => {
            card.addEventListener('click', (e) => {
                // Don't trigger if clicking the button directly
                if (e.target.classList.contains('role-button')) return;
                
                const role = card.getAttribute('data-role');
                this.handleRoleSelection(role);
            });

            // Add hover effects
            card.addEventListener('mouseenter', () => {
                card.style.cursor = 'pointer';
            });
        });

        // Smooth scrolling for navigation links
        const navLinks = document.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                if (href.startsWith('#')) {
                    e.preventDefault();
                    this.smoothScrollTo(href);
                }
            });
        });

        // Handle browser back/forward buttons
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.role) {
                this.navigateToPortal(e.state.role, false);
            }
        });
    }

    /**
     * Handle role selection and navigation to appropriate portal
     * @param {string} role - The selected role (student, teacher, admin)
     */
    handleRoleSelection(role) {
        if (!['student', 'teacher', 'admin'].includes(role)) {
            console.error('Invalid role selected:', role);
            return;
        }

        this.showLoading();
        
        // Simulate loading time for better UX
        setTimeout(() => {
            this.navigateToPortal(role);
        }, 1000);
    }

    /**
     * Navigate to the appropriate portal based on role
     * @param {string} role - The role to navigate to
     * @param {boolean} pushState - Whether to push state to history
     */
    navigateToPortal(role, pushState = true) {
        const portalUrls = {
            student: 'pages/student.html',
            teacher: 'pages/teacher.html',
            admin: 'pages/admin.html'
        };

        const url = portalUrls[role];
        if (!url) {
            console.error('No portal URL found for role:', role);
            this.hideLoading();
            return;
        }

        // Store role selection in session storage
        sessionStorage.setItem('selectedRole', role);
        sessionStorage.setItem('lastAccess', new Date().toISOString());

        // Add to browser history
        if (pushState) {
            history.pushState({ role }, `${role} Portal`, url);
        }

        // Navigate to portal
        window.location.href = url;
    }

    /**
     * Check for existing session and redirect if appropriate
     */
    checkExistingSession() {
        const savedRole = sessionStorage.getItem('selectedRole');
        const lastAccess = sessionStorage.getItem('lastAccess');

        if (savedRole && lastAccess) {
            const lastAccessTime = new Date(lastAccess);
            const now = new Date();
            const hoursSinceLastAccess = (now - lastAccessTime) / (1000 * 60 * 60);

            // If last access was within 24 hours, show option to continue
            if (hoursSinceLastAccess < 24) {
                this.showContinueSessionOption(savedRole);
            }
        }
    }

    /**
     * Show option to continue previous session
     * @param {string} role - The previous role
     */
    showContinueSessionOption(role) {
        const heroContent = document.querySelector('.hero-content');
        const continueDiv = document.createElement('div');
        continueDiv.className = 'continue-session';
        continueDiv.innerHTML = `
            <div class="continue-session-card">
                <p>Continue your previous session as <strong>${role}</strong>?</p>
                <div class="continue-session-buttons">
                    <button class="btn btn-primary" onclick="app.navigateToPortal('${role}')">
                        Continue Session
                    </button>
                    <button class="btn btn-outline" onclick="app.dismissContinueSession()">
                        Start Fresh
                    </button>
                </div>
            </div>
        `;

        // Insert before role selection
        const roleSelection = document.querySelector('.role-selection');
        heroContent.insertBefore(continueDiv, roleSelection);

        // Add styles for continue session card
        this.addContinueSessionStyles();
    }

    /**
     * Dismiss the continue session option
     */
    dismissContinueSession() {
        const continueSession = document.querySelector('.continue-session');
        if (continueSession) {
            continueSession.remove();
        }
        sessionStorage.removeItem('selectedRole');
        sessionStorage.removeItem('lastAccess');
    }

    /**
     * Add styles for continue session card
     */
    addContinueSessionStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .continue-session {
                margin-bottom: 2rem;
            }
            .continue-session-card {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(10px);
                border-radius: 1rem;
                padding: 1.5rem;
                text-align: center;
                color: var(--gray-800);
                box-shadow: var(--shadow-lg);
            }
            .continue-session-buttons {
                display: flex;
                gap: 1rem;
                justify-content: center;
                margin-top: 1rem;
            }
            @media (max-width: 480px) {
                .continue-session-buttons {
                    flex-direction: column;
                }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Smooth scroll to target element
     * @param {string} target - The target selector
     */
    smoothScrollTo(target) {
        const element = document.querySelector(target);
        if (element) {
            element.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }

    /**
     * Show loading overlay
     */
    showLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.classList.remove('hidden');
        }
    }

    /**
     * Hide loading overlay
     */
    hideLoading() {
        if (this.loadingOverlay) {
            this.loadingOverlay.classList.add('hidden');
        }
    }

    /**
     * Animate elements on page load
     */
    animateOnLoad() {
        // Add fade-in animation to role cards
        const roleCards = document.querySelectorAll('.role-card');
        roleCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 200 + (index * 150));
        });

        // Add fade-in animation to hero content
        const heroTitle = document.querySelector('.hero-title');
        const heroSubtitle = document.querySelector('.hero-subtitle');
        
        if (heroTitle) {
            heroTitle.style.opacity = '0';
            heroTitle.style.transform = 'translateY(20px)';
            setTimeout(() => {
                heroTitle.style.transition = 'all 0.6s ease-out';
                heroTitle.style.opacity = '1';
                heroTitle.style.transform = 'translateY(0)';
            }, 100);
        }

        if (heroSubtitle) {
            heroSubtitle.style.opacity = '0';
            heroSubtitle.style.transform = 'translateY(20px)';
            setTimeout(() => {
                heroSubtitle.style.transition = 'all 0.6s ease-out';
                heroSubtitle.style.opacity = '1';
                heroSubtitle.style.transform = 'translateY(0)';
            }, 300);
        }
    }

    /**
     * Utility method to get URL parameters
     * @param {string} param - Parameter name
     * @returns {string|null} Parameter value
     */
    getUrlParameter(param) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(param);
    }

    /**
     * Utility method to format dates
     * @param {Date} date - Date to format
     * @returns {string} Formatted date string
     */
    formatDate(date) {
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        }).format(date);
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = App;
}
