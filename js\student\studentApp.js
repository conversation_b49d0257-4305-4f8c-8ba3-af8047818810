/**
 * ENGLISH 2025 - Student Portal Application
 * Handles student-specific functionality and dashboard interactions
 */

class StudentApp {
    constructor() {
        this.currentUser = this.getCurrentUser();
        this.init();
    }

    /**
     * Initialize the student application
     */
    init() {
        this.setupEventListeners();
        this.loadDashboardData();
        this.animateOnLoad();
        this.checkAuthentication();
    }

    /**
     * Check if user is authenticated and has student role
     */
    checkAuthentication() {
        const selectedRole = sessionStorage.getItem('selectedRole');
        if (selectedRole !== 'student') {
            console.warn('Unauthorized access to student portal');
            window.location.href = '../index.html';
            return;
        }
    }

    /**
     * Get current user information
     */
    getCurrentUser() {
        // Mock user data - in real app this would come from authentication
        return {
            id: 'student_001',
            name: '<PERSON>',
            email: '<EMAIL>',
            currentLevel: 'B1',
            unlockedLevels: ['A1', 'A2', 'B1'],
            subscriptionType: '20', // 20 classes per month
            progress: {
                completedLessons: 12,
                unitScores: {
                    'A1_Unit1': 85,
                    'A1_Unit2': 92,
                    'A2_Unit1': 78,
                    'A2_Unit2': 88,
                    'B1_Unit1': 82,
                    'B1_Unit2': 75
                },
                overallProgress: 75
            },
            schedule: [
                {
                    id: 'class_001',
                    date: new Date(),
                    time: '14:00',
                    teacher: 'Sarah Johnson',
                    topic: 'Travel & Tourism',
                    type: 'Conversation Practice'
                }
            ],
            certificates: ['A1_Certificate', 'A2_Certificate'],
            studyStreak: 7
        };
    }

    /**
     * Set up event listeners for student portal interactions
     */
    setupEventListeners() {
        // Continue lesson button
        const continueBtn = document.querySelector('.current-lesson .btn-primary');
        if (continueBtn) {
            continueBtn.addEventListener('click', () => {
                this.continueLesson();
            });
        }

        // Join class button
        const joinClassBtn = document.querySelector('.upcoming-class .btn-secondary');
        if (joinClassBtn) {
            joinClassBtn.addEventListener('click', () => {
                this.joinClass();
            });
        }

        // AI Tutor button
        const aiTutorBtn = document.querySelector('.ai-tutor .btn-accent');
        if (aiTutorBtn) {
            aiTutorBtn.addEventListener('click', () => {
                this.startAITutor();
            });
        }

        // Quick action buttons
        const actionButtons = document.querySelectorAll('.action-btn');
        actionButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const actionText = e.target.textContent || e.target.querySelector('span:last-child').textContent;
                this.handleQuickAction(actionText.trim());
            });
        });

        // Navigation links
        const navLinks = document.querySelectorAll('.portal-nav .nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleNavigation(link.getAttribute('href'));
            });
        });
    }

    /**
     * Load and display dashboard data
     */
    loadDashboardData() {
        this.updateWelcomeMessage();
        this.updateProgressStats();
        this.updateCurrentLesson();
        this.updateUpcomingClass();
        this.updateStudyStreak();
        this.updateRecentAchievements();
    }

    /**
     * Update welcome message with user name and level
     */
    updateWelcomeMessage() {
        const welcomeTitle = document.querySelector('.welcome-content h2');
        const welcomeText = document.querySelector('.welcome-content p');
        
        if (welcomeTitle) {
            welcomeTitle.textContent = `Welcome back, ${this.currentUser.name.split(' ')[0]}!`;
        }
        
        if (welcomeText) {
            welcomeText.innerHTML = `Continue your English learning journey. You're currently at <strong>Level ${this.currentUser.currentLevel}</strong>.`;
        }
    }

    /**
     * Update progress statistics
     */
    updateProgressStats() {
        const progressStat = document.querySelector('.stat-card .stat-number');
        const lessonsStat = document.querySelectorAll('.stat-card .stat-number')[1];
        const classesStat = document.querySelectorAll('.stat-card .stat-number')[2];

        if (progressStat) {
            progressStat.textContent = `${this.currentUser.progress.overallProgress}%`;
        }
        
        if (lessonsStat) {
            lessonsStat.textContent = this.currentUser.progress.completedLessons;
        }
        
        if (classesStat) {
            classesStat.textContent = '8'; // Mock data for classes this month
        }
    }

    /**
     * Update current lesson information
     */
    updateCurrentLesson() {
        const progressFill = document.querySelector('.lesson-progress .progress-fill');
        const progressText = document.querySelector('.lesson-progress span');
        
        if (progressFill) {
            progressFill.style.width = '60%'; // Mock progress
        }
        
        if (progressText) {
            progressText.textContent = '60% Complete';
        }
    }

    /**
     * Update upcoming class information
     */
    updateUpcomingClass() {
        const classTime = document.querySelector('.class-time .time');
        const classDate = document.querySelector('.class-time .date');
        const teacherName = document.querySelector('.class-details p');
        
        if (this.currentUser.schedule.length > 0) {
            const nextClass = this.currentUser.schedule[0];
            
            if (classTime) {
                classTime.textContent = nextClass.time;
            }
            
            if (classDate) {
                classDate.textContent = this.formatDate(nextClass.date);
            }
            
            if (teacherName) {
                teacherName.textContent = `Teacher: ${nextClass.teacher}`;
            }
        }
    }

    /**
     * Update study streak display
     */
    updateStudyStreak() {
        const streakNumber = document.querySelector('.streak-number');
        const streakDays = document.querySelectorAll('.streak-calendar .day');
        
        if (streakNumber) {
            streakNumber.textContent = this.currentUser.studyStreak;
        }
        
        // Update streak calendar (mock data)
        streakDays.forEach((day, index) => {
            if (index < this.currentUser.studyStreak) {
                day.classList.add('completed');
            }
        });
    }

    /**
     * Update recent achievements
     */
    updateRecentAchievements() {
        // This would typically fetch real achievement data
        // For now, the achievements are static in the HTML
    }

    /**
     * Handle continue lesson action
     */
    continueLesson() {
        console.log('Continuing lesson...');
        this.showNotification('Loading lesson...', 'info');
        
        // Simulate navigation to lesson
        setTimeout(() => {
            this.showNotification('Lesson loaded successfully!', 'success');
            // In real app: window.location.href = 'lesson.html?unit=3&lesson=2';
        }, 1500);
    }

    /**
     * Handle join class action
     */
    joinClass() {
        const now = new Date();
        const classTime = new Date();
        classTime.setHours(14, 0, 0, 0); // 2:00 PM
        
        const timeDiff = classTime.getTime() - now.getTime();
        const minutesDiff = Math.floor(timeDiff / (1000 * 60));
        
        if (minutesDiff > 15) {
            this.showNotification(`Class starts in ${minutesDiff} minutes. You can join 15 minutes before.`, 'warning');
        } else {
            this.showNotification('Launching Zoom meeting...', 'info');
            setTimeout(() => {
                this.showNotification('Zoom meeting opened in new window!', 'success');
                // In real app: window.open('zoom_meeting_url');
            }, 1000);
        }
    }

    /**
     * Handle AI tutor action
     */
    startAITutor() {
        this.showNotification('Starting AI Tutor...', 'info');
        
        setTimeout(() => {
            this.showNotification('AI Tutor is ready! Start speaking to practice.', 'success');
            // In real app: navigate to AI tutor interface
        }, 1500);
    }

    /**
     * Handle quick action buttons
     */
    handleQuickAction(action) {
        const actions = {
            'Browse Courses': () => this.showNotification('Loading course catalog...', 'info'),
            'Schedule Class': () => this.showNotification('Opening calendar...', 'info'),
            'View Progress': () => this.showNotification('Loading progress report...', 'info'),
            'Daily News': () => this.showNotification('Loading daily news...', 'info')
        };

        if (actions[action]) {
            actions[action]();
        } else {
            this.showNotification(`${action} feature coming soon!`, 'info');
        }
    }

    /**
     * Handle navigation
     */
    handleNavigation(href) {
        const section = href.replace('#', '');
        this.showNotification(`Navigating to ${section}...`, 'info');
        
        // In real app, this would show different sections of the portal
        setTimeout(() => {
            this.showNotification(`${section} section loaded!`, 'success');
        }, 1000);
    }

    /**
     * Show notification to user
     */
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // Add styles if not already added
        this.addNotificationStyles();

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);

        // Add close button functionality
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });
    }

    /**
     * Add notification styles
     */
    addNotificationStyles() {
        if (document.getElementById('notification-styles')) return;

        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                padding: 1rem;
                border-radius: 0.5rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                animation: slideIn 0.3s ease-out;
            }
            .notification-info {
                background: var(--info-color);
                color: white;
            }
            .notification-success {
                background: var(--success-color);
                color: white;
            }
            .notification-warning {
                background: var(--warning-color);
                color: white;
            }
            .notification-error {
                background: var(--error-color);
                color: white;
            }
            .notification-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .notification-close {
                background: none;
                border: none;
                color: inherit;
                font-size: 1.2rem;
                cursor: pointer;
                padding: 0;
                margin-left: 1rem;
            }
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Animate elements on page load
     */
    animateOnLoad() {
        const cards = document.querySelectorAll('.dashboard-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100 + (index * 100));
        });
    }

    /**
     * Format date for display
     */
    formatDate(date) {
        const today = new Date();
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        if (date.toDateString() === today.toDateString()) {
            return 'Today, ' + date.toLocaleDateString('en-US', { month: 'long', day: 'numeric' });
        } else if (date.toDateString() === tomorrow.toDateString()) {
            return 'Tomorrow, ' + date.toLocaleDateString('en-US', { month: 'long', day: 'numeric' });
        } else {
            return date.toLocaleDateString('en-US', { 
                weekday: 'long', 
                month: 'long', 
                day: 'numeric' 
            });
        }
    }
}

// Initialize the student application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.studentApp = new StudentApp();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StudentApp;
}
