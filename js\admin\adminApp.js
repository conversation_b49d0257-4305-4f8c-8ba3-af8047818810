/**
 * ENGLISH 2025 - Admin Portal Application
 * Handles admin-specific functionality and system monitoring
 */

class AdminApp {
    constructor() {
        this.currentUser = this.getCurrentUser();
        this.systemMetrics = this.getSystemMetrics();
        this.init();
    }

    /**
     * Initialize the admin application
     */
    init() {
        this.setupEventListeners();
        this.loadDashboardData();
        this.animateOnLoad();
        this.checkAuthentication();
        this.startRealTimeUpdates();
    }

    /**
     * Check if user is authenticated and has admin role
     */
    checkAuthentication() {
        const selectedRole = sessionStorage.getItem('selectedRole');
        if (selectedRole !== 'admin') {
            console.warn('Unauthorized access to admin portal');
            window.location.href = '../index.html';
            return;
        }
    }

    /**
     * Get current user information
     */
    getCurrentUser() {
        // Mock admin data - in real app this would come from authentication
        return {
            id: 'admin_001',
            name: '<PERSON>',
            email: 'micha<PERSON>.<EMAIL>',
            role: 'System Administrator',
            permissions: ['user_management', 'system_monitoring', 'content_management', 'analytics']
        };
    }

    /**
     * Get system metrics
     */
    getSystemMetrics() {
        return {
            activeUsers: 1247,
            monthlyRevenue: 24580,
            lessonsCompleted: 3456,
            studentSatisfaction: 94.2,
            systemHealth: {
                uptime: 99.9,
                responseTime: 245,
                errorRate: 0.02,
                activeSessions: 1247
            },
            users: {
                students: 1156,
                teachers: 45,
                admins: 8
            },
            supportTickets: {
                open: 12,
                inProgress: 8,
                resolvedToday: 45
            }
        };
    }

    /**
     * Set up event listeners for admin portal interactions
     */
    setupEventListeners() {
        // Metric cards
        const metricCards = document.querySelectorAll('.metric-card');
        metricCards.forEach(card => {
            card.addEventListener('click', () => {
                const metricLabel = card.querySelector('.metric-label').textContent;
                this.handleMetricClick(metricLabel);
            });
        });

        // User management buttons
        const userButtons = document.querySelectorAll('.users-card .btn');
        userButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                this.handleUserAction(btn.textContent.trim());
            });
        });

        // Teacher management buttons
        const teacherButtons = document.querySelectorAll('.teachers-card .btn');
        teacherButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                this.handleTeacherAction(btn.textContent.trim());
            });
        });

        // Support ticket buttons
        const supportButtons = document.querySelectorAll('.support-card .btn');
        supportButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                this.handleSupportAction(btn.textContent.trim());
            });
        });

        // Quick action buttons
        const actionButtons = document.querySelectorAll('.action-btn');
        actionButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const actionText = btn.textContent.trim();
                this.handleQuickAction(actionText);
            });
        });

        // Navigation links
        const navLinks = document.querySelectorAll('.portal-nav .nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleNavigation(link.getAttribute('href'));
            });
        });

        // System health items
        const healthItems = document.querySelectorAll('.health-item');
        healthItems.forEach(item => {
            item.addEventListener('click', () => {
                const healthLabel = item.querySelector('.health-label').textContent;
                this.handleHealthMetricClick(healthLabel);
            });
        });
    }

    /**
     * Load and display dashboard data
     */
    loadDashboardData() {
        this.updateSystemStatus();
        this.updateMetrics();
        this.updateUserStats();
        this.updateSystemHealth();
        this.updateSupportTickets();
        this.updateRevenueData();
    }

    /**
     * Update system status indicator
     */
    updateSystemStatus() {
        const statusIndicator = document.querySelector('.status-indicator');
        const statusText = statusIndicator.querySelector('span');
        
        // Simulate system health check
        const isHealthy = this.systemMetrics.systemHealth.uptime > 99.0;
        
        if (isHealthy) {
            statusIndicator.className = 'status-indicator online';
            statusText.textContent = 'All Systems Operational';
        } else {
            statusIndicator.className = 'status-indicator warning';
            statusText.textContent = 'System Issues Detected';
        }
    }

    /**
     * Update key metrics display
     */
    updateMetrics() {
        const metricNumbers = document.querySelectorAll('.metric-number');
        
        if (metricNumbers[0]) {
            metricNumbers[0].textContent = this.formatNumber(this.systemMetrics.activeUsers);
        }
        
        if (metricNumbers[1]) {
            metricNumbers[1].textContent = `$${this.formatNumber(this.systemMetrics.monthlyRevenue)}`;
        }
        
        if (metricNumbers[2]) {
            metricNumbers[2].textContent = this.formatNumber(this.systemMetrics.lessonsCompleted);
        }
        
        if (metricNumbers[3]) {
            metricNumbers[3].textContent = `${this.systemMetrics.studentSatisfaction}%`;
        }
    }

    /**
     * Update user statistics
     */
    updateUserStats() {
        const userCounts = document.querySelectorAll('.user-count');
        
        if (userCounts[0]) {
            userCounts[0].textContent = this.formatNumber(this.systemMetrics.users.students);
        }
        
        if (userCounts[1]) {
            userCounts[1].textContent = this.systemMetrics.users.teachers;
        }
        
        if (userCounts[2]) {
            userCounts[2].textContent = this.systemMetrics.users.admins;
        }
    }

    /**
     * Update system health metrics
     */
    updateSystemHealth() {
        const healthValues = document.querySelectorAll('.health-value');
        
        if (healthValues[0]) {
            healthValues[0].textContent = `${this.systemMetrics.systemHealth.uptime}%`;
        }
        
        if (healthValues[1]) {
            healthValues[1].textContent = `${this.systemMetrics.systemHealth.responseTime}ms`;
        }
        
        if (healthValues[2]) {
            healthValues[2].textContent = `${this.systemMetrics.systemHealth.errorRate}%`;
        }
        
        if (healthValues[3]) {
            healthValues[3].textContent = this.formatNumber(this.systemMetrics.systemHealth.activeSessions);
        }
    }

    /**
     * Update support ticket statistics
     */
    updateSupportTickets() {
        const ticketNumbers = document.querySelectorAll('.ticket-number');
        
        if (ticketNumbers[0]) {
            ticketNumbers[0].textContent = this.systemMetrics.supportTickets.open;
        }
        
        if (ticketNumbers[1]) {
            ticketNumbers[1].textContent = this.systemMetrics.supportTickets.inProgress;
        }
        
        if (ticketNumbers[2]) {
            ticketNumbers[2].textContent = this.systemMetrics.supportTickets.resolvedToday;
        }
    }

    /**
     * Update revenue data
     */
    updateRevenueData() {
        // Revenue breakdown is already populated in HTML with mock data
        // In real app, this would dynamically populate from actual revenue data
    }

    /**
     * Handle metric card clicks
     */
    handleMetricClick(metricLabel) {
        this.showNotification(`Loading detailed ${metricLabel} analytics...`, 'info');
        
        setTimeout(() => {
            this.showNotification(`${metricLabel} analytics loaded!`, 'success');
        }, 1000);
    }

    /**
     * Handle user management actions
     */
    handleUserAction(action) {
        const actions = {
            'Manage Users': () => {
                this.showNotification('Loading user management interface...', 'info');
                setTimeout(() => {
                    this.showNotification('User management interface loaded!', 'success');
                }, 1000);
            }
        };

        if (actions[action]) {
            actions[action]();
        } else {
            this.showNotification(`${action} feature coming soon!`, 'info');
        }
    }

    /**
     * Handle teacher management actions
     */
    handleTeacherAction(action) {
        const actions = {
            'View All Teachers': () => {
                this.showNotification('Loading complete teacher roster...', 'info');
                setTimeout(() => {
                    this.showNotification('Teacher roster loaded!', 'success');
                }, 1000);
            }
        };

        if (actions[action]) {
            actions[action]();
        } else {
            this.showNotification(`${action} feature coming soon!`, 'info');
        }
    }

    /**
     * Handle support ticket actions
     */
    handleSupportAction(action) {
        const actions = {
            'View All Tickets': () => {
                this.showNotification('Loading support ticket system...', 'info');
                setTimeout(() => {
                    this.showNotification('Support ticket system loaded!', 'success');
                }, 1000);
            }
        };

        if (actions[action]) {
            actions[action]();
        } else {
            this.showNotification(`${action} feature coming soon!`, 'info');
        }
    }

    /**
     * Handle quick action buttons
     */
    handleQuickAction(action) {
        const actions = {
            'Add New User': () => this.showNotification('Opening user creation form...', 'info'),
            'Generate Report': () => this.showNotification('Generating system report...', 'info'),
            'Send Announcement': () => this.showNotification('Opening announcement composer...', 'info'),
            'System Settings': () => this.showNotification('Loading system settings...', 'info'),
            'Backup Data': () => this.showNotification('Starting data backup...', 'info'),
            'Update Content': () => this.showNotification('Opening content management...', 'info')
        };

        if (actions[action]) {
            actions[action]();
            setTimeout(() => {
                this.showNotification(`${action} completed successfully!`, 'success');
            }, 2000);
        } else {
            this.showNotification(`${action} feature coming soon!`, 'info');
        }
    }

    /**
     * Handle health metric clicks
     */
    handleHealthMetricClick(metricLabel) {
        this.showNotification(`Loading detailed ${metricLabel} monitoring...`, 'info');
        
        setTimeout(() => {
            this.showNotification(`${metricLabel} monitoring dashboard loaded!`, 'success');
        }, 1000);
    }

    /**
     * Handle navigation
     */
    handleNavigation(href) {
        const section = href.replace('#', '');
        this.showNotification(`Navigating to ${section}...`, 'info');
        
        // In real app, this would show different sections of the portal
        setTimeout(() => {
            this.showNotification(`${section} section loaded!`, 'success');
        }, 1000);
    }

    /**
     * Start real-time updates for metrics
     */
    startRealTimeUpdates() {
        // Simulate real-time metric updates every 30 seconds
        setInterval(() => {
            this.updateRealTimeMetrics();
        }, 30000);
    }

    /**
     * Update metrics with simulated real-time data
     */
    updateRealTimeMetrics() {
        // Simulate small changes in metrics
        this.systemMetrics.activeUsers += Math.floor(Math.random() * 10) - 5;
        this.systemMetrics.systemHealth.activeSessions = this.systemMetrics.activeUsers;
        this.systemMetrics.systemHealth.responseTime += Math.floor(Math.random() * 20) - 10;
        
        // Update display
        this.updateMetrics();
        this.updateSystemHealth();
    }

    /**
     * Format numbers for display
     */
    formatNumber(num) {
        return new Intl.NumberFormat().format(num);
    }

    /**
     * Show notification to user
     */
    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <span class="notification-message">${message}</span>
                <button class="notification-close">&times;</button>
            </div>
        `;

        // Add styles if not already added
        this.addNotificationStyles();

        // Add to page
        document.body.appendChild(notification);

        // Auto remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);

        // Add close button functionality
        const closeBtn = notification.querySelector('.notification-close');
        closeBtn.addEventListener('click', () => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        });
    }

    /**
     * Add notification styles
     */
    addNotificationStyles() {
        if (document.getElementById('notification-styles')) return;

        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                padding: 1rem;
                border-radius: 0.5rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                animation: slideIn 0.3s ease-out;
            }
            .notification-info {
                background: var(--info-color);
                color: white;
            }
            .notification-success {
                background: var(--success-color);
                color: white;
            }
            .notification-warning {
                background: var(--warning-color);
                color: white;
            }
            .notification-error {
                background: var(--error-color);
                color: white;
            }
            .notification-content {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .notification-close {
                background: none;
                border: none;
                color: inherit;
                font-size: 1.2rem;
                cursor: pointer;
                padding: 0;
                margin-left: 1rem;
            }
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * Animate elements on page load
     */
    animateOnLoad() {
        const cards = document.querySelectorAll('.dashboard-card, .metric-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease-out';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 50 + (index * 50));
        });
    }
}

// Initialize the admin application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.adminApp = new AdminApp();
});

// Export for module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdminApp;
}
